import React, { createContext, useContext, useEffect, useState } from "react";
import { useUser } from "@clerk/clerk-expo";
import { supabase } from "@/lib/supabase";
import { router } from "expo-router";

interface AuthContextType {
  user: any;
  isLoading: boolean;
  supabaseUser: any;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isLoading: true,
  supabaseUser: null,
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoaded } = useUser();
  const [supabaseUser, setSupabaseUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isLoaded) {
      if (user) {
        // User is signed in, sync with Supabase
        syncUserWithSupabase();
      } else {
        // User is not signed in, redirect to auth
        router.replace("/(auth)/sign-in");
      }
      setIsLoading(false);
    }
  }, [user, isLoaded]);

  const syncUserWithSupabase = async () => {
    if (!user) return;

    try {
      // Check if user exists in Supabase
      const { data: existingUser, error: fetchError } = await supabase
        .from("users")
        .select("*")
        .eq("clerk_user_id", user.id)
        .single();

      if (fetchError && fetchError.code !== "PGRST116") {
        console.error("Error fetching user:", fetchError);
        return;
      }

      if (!existingUser) {
        // Create new user in Supabase
        const { data: newUser, error: insertError } = await supabase
          .from("users")
          .insert({
            clerk_user_id: user.id,
            email: user.emailAddresses[0]?.emailAddress || "",
            full_name: user.fullName,
            subscription_tier: "free",
          })
          .select()
          .single();

        if (insertError) {
          console.error("Error creating user:", insertError);
          return;
        }

        setSupabaseUser(newUser);
      } else {
        // Update existing user
        const { data: updatedUser, error: updateError } = await supabase
          .from("users")
          .update({
            email: user.emailAddresses[0]?.emailAddress || "",
            full_name: user.fullName,
          })
          .eq("clerk_user_id", user.id)
          .select()
          .single();

        if (updateError) {
          console.error("Error updating user:", updateError);
          return;
        }

        setSupabaseUser(updatedUser);
      }
    } catch (error) {
      console.error("Error syncing user with Supabase:", error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        supabaseUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
