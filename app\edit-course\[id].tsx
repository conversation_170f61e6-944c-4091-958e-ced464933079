import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface Course {
  id: string;
  title: string;
  description: string;
  syllabus_content: string;
  difficulty_level: "beginner" | "intermediate" | "advanced";
  estimated_duration_weeks: number;
}

export default function EditCourseScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useUser();
  const [course, setCourse] = useState<Course | null>(null);
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [syllabusContent, setSyllabusContent] = useState("");
  const [difficultyLevel, setDifficultyLevel] = useState<
    "beginner" | "intermediate" | "advanced"
  >("beginner");
  const [estimatedWeeks, setEstimatedWeeks] = useState("4");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (user && id) {
      loadCourse();
    }
  }, [user, id]);

  const loadCourse = async () => {
    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const { data: courseData, error } = await supabase
        .from("courses")
        .select("*")
        .eq("id", id)
        .eq("user_id", supabaseUser.id)
        .single();

      if (error) {
        console.error("Error loading course:", error);
        Alert.alert("Error", "Course not found");
        router.back();
        return;
      }

      setCourse(courseData);
      setTitle(courseData.title);
      setDescription(courseData.description);
      setSyllabusContent(courseData.syllabus_content);
      setDifficultyLevel(courseData.difficulty_level);
      setEstimatedWeeks(courseData.estimated_duration_weeks.toString());
    } catch (error) {
      console.error("Error loading course:", error);
      Alert.alert("Error", "Failed to load course");
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const saveCourse = async () => {
    if (!title.trim() || !description.trim()) {
      Alert.alert("Error", "Please fill in all required fields");
      return;
    }

    setSaving(true);

    try {
      const { error } = await supabase
        .from("courses")
        .update({
          title: title.trim(),
          description: description.trim(),
          syllabus_content: syllabusContent.trim(),
          difficulty_level: difficultyLevel,
          estimated_duration_weeks: parseInt(estimatedWeeks) || 4,
        })
        .eq("id", id);

      if (error) throw error;

      Alert.alert("Success", "Course updated successfully!", [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      console.error("Error updating course:", error);
      Alert.alert("Error", "Failed to update course");
    } finally {
      setSaving(false);
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case "beginner":
        return "#34C759";
      case "intermediate":
        return "#FF9500";
      case "advanced":
        return "#FF3B30";
      default:
        return "#007AFF";
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading course...</Text>
      </View>
    );
  }

  if (!course) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>Course not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerBackButton}
          onPress={() => router.back()}
        >
          <Text style={styles.headerBackText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Course</Text>
        <TouchableOpacity
          style={[styles.saveButton, saving && styles.saveButtonDisabled]}
          onPress={saveCourse}
          disabled={saving}
        >
          <Text style={styles.saveButtonText}>
            {saving ? "Saving..." : "Save"}
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.formSection}>
          <Text style={styles.label}>Course Title *</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter course title..."
            value={title}
            onChangeText={setTitle}
          />
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Description *</Text>
          <TextInput
            style={styles.textAreaInput}
            placeholder="Enter course description..."
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={4}
          />
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Syllabus Content</Text>
          <TextInput
            style={styles.textAreaInput}
            placeholder="Enter detailed syllabus content..."
            value={syllabusContent}
            onChangeText={setSyllabusContent}
            multiline
            numberOfLines={6}
          />
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Difficulty Level</Text>
          <View style={styles.difficultyContainer}>
            {(["beginner", "intermediate", "advanced"] as const).map(
              (level) => (
                <TouchableOpacity
                  key={level}
                  style={[
                    styles.difficultyButton,
                    difficultyLevel === level && styles.selectedDifficulty,
                    { borderColor: getDifficultyColor(level) },
                    difficultyLevel === level && {
                      backgroundColor: getDifficultyColor(level),
                    },
                  ]}
                  onPress={() => setDifficultyLevel(level)}
                >
                  <Text
                    style={[
                      styles.difficultyText,
                      difficultyLevel === level &&
                        styles.selectedDifficultyText,
                    ]}
                  >
                    {level.charAt(0).toUpperCase() + level.slice(1)}
                  </Text>
                </TouchableOpacity>
              )
            )}
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Estimated Duration (weeks)</Text>
          <TextInput
            style={styles.numberInput}
            placeholder="4"
            value={estimatedWeeks}
            onChangeText={setEstimatedWeeks}
            keyboardType="numeric"
          />
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>Course Information</Text>
          <Text style={styles.infoText}>
            Created: {new Date(course.created_at).toLocaleDateString()}
          </Text>
          <Text style={styles.infoText}>Course ID: {course.id}</Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  headerBackButton: {
    padding: 5,
  },
  headerBackText: {
    fontSize: 16,
    color: "#007AFF",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  saveButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  saveButtonDisabled: {
    backgroundColor: "#ccc",
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  errorText: {
    fontSize: 18,
    color: "#666",
    marginBottom: 20,
    textAlign: "center",
  },
  backButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  content: {
    flex: 1,
    padding: 20,
  },
  formSection: {
    marginBottom: 25,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    color: "#333",
    backgroundColor: "#fff",
    height: 50,
  },
  textAreaInput: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    color: "#333",
    backgroundColor: "#fff",
    textAlignVertical: "top",
    minHeight: 100,
  },
  numberInput: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    color: "#333",
    backgroundColor: "#fff",
    height: 50,
    width: 100,
  },
  difficultyContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  difficultyButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderWidth: 2,
    borderRadius: 8,
    marginHorizontal: 5,
    alignItems: "center",
  },
  selectedDifficulty: {
    backgroundColor: "#007AFF",
  },
  difficultyText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
  },
  selectedDifficultyText: {
    color: "#fff",
  },
  infoSection: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    marginTop: 20,
    marginBottom: 40,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 5,
  },
});
