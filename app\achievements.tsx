import {
  achievementService,
  LearningGoal,
  UserAchievement,
} from "@/lib/achievements";
import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

export default function AchievementsScreen() {
  const { user } = useUser();
  const [achievements, setAchievements] = useState<UserAchievement[]>([]);
  const [goals, setGoals] = useState<LearningGoal[]>([]);
  const [stats, setStats] = useState({
    totalPoints: 0,
    achievementsEarned: 0,
    totalAchievements: 0,
  });
  const [activeTab, setActiveTab] = useState<"achievements" | "goals">(
    "achievements"
  );
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const [userAchievements, userGoals, userStats] = await Promise.all([
        achievementService.getUserAchievements(supabaseUser.id),
        achievementService.getLearningGoals(supabaseUser.id),
        achievementService.getUserStats(supabaseUser.id),
      ]);

      setAchievements(userAchievements);
      setGoals(userGoals);
      setStats(userStats);

      // Check for new achievements
      await achievementService.checkAndAwardAchievements(supabaseUser.id);
    } catch (error) {
      console.error("Error loading achievements data:", error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const createGoal = () => {
    Alert.alert("Create Learning Goal", "Choose a goal type:", [
      {
        text: "Study Time",
        onPress: () => showGoalInput("study_time", "minutes"),
      },
      {
        text: "Flashcards",
        onPress: () => showGoalInput("flashcards", "cards"),
      },
      {
        text: "Study Streak",
        onPress: () => showGoalInput("streak", "days"),
      },
      { text: "Cancel", style: "cancel" },
    ]);
  };

  const showGoalInput = (type: string, unit: string) => {
    Alert.prompt(
      "Set Goal",
      `How many ${unit} do you want to achieve?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Create",
          onPress: async (value) => {
            if (!value || isNaN(Number(value))) {
              Alert.alert("Error", "Please enter a valid number");
              return;
            }

            const { data: supabaseUser } = await supabase
              .from("users")
              .select("id")
              .eq("clerk_user_id", user?.id)
              .single();

            if (!supabaseUser) return;

            const goal = await achievementService.createLearningGoal(
              supabaseUser.id,
              {
                title: `${type
                  .replace("_", " ")
                  .replace(/\b\w/g, (l) => l.toUpperCase())} Goal`,
                description: `Achieve ${value} ${unit}`,
                target_type: type as any,
                target_value: Number(value),
                status: "active",
              }
            );

            if (goal) {
              await loadData();
              Alert.alert("Success", "Goal created successfully!");
            }
          },
        },
      ],
      "plain-text"
    );
  };

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "#34C759";
      case "active":
        return "#007AFF";
      case "paused":
        return "#FF9500";
      case "cancelled":
        return "#FF3B30";
      default:
        return "#666";
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading achievements...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Achievements</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{stats.totalPoints}</Text>
          <Text style={styles.statLabel}>Points</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {stats.achievementsEarned}/{stats.totalAchievements}
          </Text>
          <Text style={styles.statLabel}>Achievements</Text>
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {Math.round(
              (stats.achievementsEarned / stats.totalAchievements) * 100
            ) || 0}
            %
          </Text>
          <Text style={styles.statLabel}>Complete</Text>
        </View>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "achievements" && styles.activeTab]}
          onPress={() => setActiveTab("achievements")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "achievements" && styles.activeTabText,
            ]}
          >
            Achievements
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "goals" && styles.activeTab]}
          onPress={() => setActiveTab("goals")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "goals" && styles.activeTabText,
            ]}
          >
            Goals
          </Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {activeTab === "achievements" && (
          <View style={styles.achievementsContainer}>
            {achievements.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyTitle}>No achievements yet</Text>
                <Text style={styles.emptyDescription}>
                  Keep studying to unlock your first achievement!
                </Text>
              </View>
            ) : (
              achievements.map((achievement) => (
                <View key={achievement.id} style={styles.achievementItem}>
                  <Text style={styles.achievementIcon}>
                    {achievement.achievement?.icon}
                  </Text>
                  <View style={styles.achievementInfo}>
                    <Text style={styles.achievementName}>
                      {achievement.achievement?.name}
                    </Text>
                    <Text style={styles.achievementDescription}>
                      {achievement.achievement?.description}
                    </Text>
                    <Text style={styles.achievementDate}>
                      Earned{" "}
                      {new Date(achievement.earned_at).toLocaleDateString()}
                    </Text>
                  </View>
                  <View style={styles.achievementPoints}>
                    <Text style={styles.pointsText}>
                      +{achievement.achievement?.points}
                    </Text>
                  </View>
                </View>
              ))
            )}
          </View>
        )}

        {activeTab === "goals" && (
          <View style={styles.goalsContainer}>
            <TouchableOpacity
              style={styles.createGoalButton}
              onPress={createGoal}
            >
              <Text style={styles.createGoalText}>+ Create New Goal</Text>
            </TouchableOpacity>

            {goals.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyTitle}>No goals set</Text>
                <Text style={styles.emptyDescription}>
                  Create your first learning goal to stay motivated!
                </Text>
              </View>
            ) : (
              goals.map((goal) => (
                <View key={goal.id} style={styles.goalItem}>
                  <View style={styles.goalHeader}>
                    <Text style={styles.goalTitle}>{goal.title}</Text>
                    <View
                      style={[
                        styles.statusBadge,
                        { backgroundColor: getStatusColor(goal.status) },
                      ]}
                    >
                      <Text style={styles.statusText}>
                        {goal.status.toUpperCase()}
                      </Text>
                    </View>
                  </View>

                  {goal.description && (
                    <Text style={styles.goalDescription}>
                      {goal.description}
                    </Text>
                  )}

                  <View style={styles.progressContainer}>
                    <View style={styles.progressBar}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            width: `${getProgressPercentage(
                              goal.current_progress,
                              goal.target_value
                            )}%`,
                          },
                        ]}
                      />
                    </View>
                    <Text style={styles.progressText}>
                      {goal.current_progress} / {goal.target_value}
                    </Text>
                  </View>

                  {goal.deadline && (
                    <Text style={styles.deadlineText}>
                      Deadline: {new Date(goal.deadline).toLocaleDateString()}
                    </Text>
                  )}
                </View>
              ))
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    fontSize: 16,
    color: "#007AFF",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  placeholder: {
    width: 50,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: "#fff",
    paddingVertical: 20,
    marginBottom: 10,
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#007AFF",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    marginHorizontal: 20,
    borderRadius: 8,
    marginBottom: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTab: {
    borderBottomColor: "#007AFF",
  },
  tabText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  activeTabText: {
    color: "#007AFF",
    fontWeight: "600",
  },
  content: {
    flex: 1,
  },
  achievementsContainer: {
    padding: 20,
  },
  achievementItem: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  achievementIcon: {
    fontSize: 32,
    marginRight: 15,
  },
  achievementInfo: {
    flex: 1,
  },
  achievementName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  achievementDate: {
    fontSize: 12,
    color: "#999",
  },
  achievementPoints: {
    backgroundColor: "#007AFF",
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  pointsText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  goalsContainer: {
    padding: 20,
  },
  createGoalButton: {
    backgroundColor: "#007AFF",
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
    marginBottom: 20,
  },
  createGoalText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  goalItem: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 15,
    marginBottom: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  goalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  goalTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "600",
  },
  goalDescription: {
    fontSize: 14,
    color: "#666",
    marginBottom: 10,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressBar: {
    height: 6,
    backgroundColor: "#e0e0e0",
    borderRadius: 3,
    marginBottom: 4,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#34C759",
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: "#666",
    textAlign: "right",
  },
  deadlineText: {
    fontSize: 12,
    color: "#FF9500",
    fontWeight: "500",
  },
  emptyContainer: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 30,
    alignItems: "center",
    marginTop: 50,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
});
