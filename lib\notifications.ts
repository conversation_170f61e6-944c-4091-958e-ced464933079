import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface ReminderSettings {
  dailyStudyReminder: boolean;
  dailyStudyTime: string; // HH:MM format
  flashcardReminder: boolean;
  flashcardInterval: number; // hours
  weeklyGoalReminder: boolean;
  weeklyGoalDay: number; // 0-6 (Sunday-Saturday)
}

export class NotificationService {
  private static instance: NotificationService;
  private isInitialized = false;

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  async initialize(): Promise<boolean> {
    if (this.isInitialized) return true;

    try {
      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Notification permissions not granted');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('study-reminders', {
          name: 'Study Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#007AFF',
        });
      }

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Error initializing notifications:', error);
      return false;
    }
  }

  async scheduleStudyReminder(time: string): Promise<string | null> {
    try {
      const [hours, minutes] = time.split(':').map(Number);
      
      const trigger = {
        hour: hours,
        minute: minutes,
        repeats: true,
      };

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '📚 Time to Study!',
          body: 'Your daily study session is ready. Let\'s continue your learning journey!',
          data: { type: 'daily_study' },
        },
        trigger,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling study reminder:', error);
      return null;
    }
  }

  async scheduleFlashcardReminder(intervalHours: number): Promise<string | null> {
    try {
      const trigger = {
        seconds: intervalHours * 3600,
        repeats: true,
      };

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧠 Flashcard Review Time!',
          body: 'You have flashcards ready for review. Keep your memory sharp!',
          data: { type: 'flashcard_review' },
        },
        trigger,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling flashcard reminder:', error);
      return null;
    }
  }

  async scheduleWeeklyGoalReminder(dayOfWeek: number): Promise<string | null> {
    try {
      const trigger = {
        weekday: dayOfWeek + 1, // Expo uses 1-7 (Sunday-Saturday)
        hour: 9,
        minute: 0,
        repeats: true,
      };

      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🎯 Weekly Goal Check-in',
          body: 'How did your learning week go? Review your progress and set new goals!',
          data: { type: 'weekly_goal' },
        },
        trigger,
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling weekly goal reminder:', error);
      return null;
    }
  }

  async scheduleCustomReminder(
    title: string,
    body: string,
    triggerDate: Date,
    data?: any
  ): Promise<string | null> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: { type: 'custom', ...data },
        },
        trigger: { date: triggerDate },
      });

      return notificationId;
    } catch (error) {
      console.error('Error scheduling custom reminder:', error);
      return null;
    }
  }

  async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Error canceling notification:', error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling all notifications:', error);
    }
  }

  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  async saveReminderSettings(settings: ReminderSettings): Promise<void> {
    try {
      await AsyncStorage.setItem('reminderSettings', JSON.stringify(settings));
      await this.updateNotificationsFromSettings(settings);
    } catch (error) {
      console.error('Error saving reminder settings:', error);
    }
  }

  async getReminderSettings(): Promise<ReminderSettings> {
    try {
      const settingsJson = await AsyncStorage.getItem('reminderSettings');
      if (settingsJson) {
        return JSON.parse(settingsJson);
      }
    } catch (error) {
      console.error('Error getting reminder settings:', error);
    }

    // Return default settings
    return {
      dailyStudyReminder: true,
      dailyStudyTime: '19:00',
      flashcardReminder: true,
      flashcardInterval: 24,
      weeklyGoalReminder: true,
      weeklyGoalDay: 0, // Sunday
    };
  }

  private async updateNotificationsFromSettings(settings: ReminderSettings): Promise<void> {
    // Cancel existing notifications
    await this.cancelAllNotifications();

    // Schedule new notifications based on settings
    if (settings.dailyStudyReminder) {
      await this.scheduleStudyReminder(settings.dailyStudyTime);
    }

    if (settings.flashcardReminder) {
      await this.scheduleFlashcardReminder(settings.flashcardInterval);
    }

    if (settings.weeklyGoalReminder) {
      await this.scheduleWeeklyGoalReminder(settings.weeklyGoalDay);
    }
  }

  async sendImmediateNotification(title: string, body: string, data?: any): Promise<void> {
    try {
      await Notifications.presentNotificationAsync({
        title,
        body,
        data,
      });
    } catch (error) {
      console.error('Error sending immediate notification:', error);
    }
  }

  // Study streak notifications
  async notifyStudyStreak(streakDays: number): Promise<void> {
    const milestones = [7, 14, 30, 60, 100];
    
    if (milestones.includes(streakDays)) {
      await this.sendImmediateNotification(
        `🔥 ${streakDays} Day Streak!`,
        `Amazing! You've studied for ${streakDays} days in a row. Keep up the great work!`,
        { type: 'streak_milestone', days: streakDays }
      );
    }
  }

  // Course completion notifications
  async notifyCourseCompletion(courseTitle: string): Promise<void> {
    await this.sendImmediateNotification(
      '🎉 Course Completed!',
      `Congratulations! You've completed "${courseTitle}". Time to celebrate your achievement!`,
      { type: 'course_completion', course: courseTitle }
    );
  }

  // Flashcard mastery notifications
  async notifyFlashcardMastery(category: string, masteredCount: number): Promise<void> {
    await this.sendImmediateNotification(
      '🧠 Mastery Achieved!',
      `You've mastered ${masteredCount} flashcards in ${category}. Your knowledge is growing!`,
      { type: 'flashcard_mastery', category, count: masteredCount }
    );
  }
}

export const notificationService = NotificationService.getInstance();
