import { supabase } from './supabase';
import { notificationService } from './notifications';

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'study_streak' | 'flashcards' | 'courses' | 'time' | 'performance';
  requirement_type: 'count' | 'streak' | 'percentage' | 'time';
  requirement_value: number;
  points: number;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  earned_at: string;
  progress: number;
  achievement?: Achievement;
}

export interface LearningGoal {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  target_type: 'study_time' | 'flashcards' | 'courses' | 'streak';
  target_value: number;
  current_progress: number;
  deadline?: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  created_at: string;
}

export class AchievementService {
  private static instance: AchievementService;

  static getInstance(): AchievementService {
    if (!AchievementService.instance) {
      AchievementService.instance = new AchievementService();
    }
    return AchievementService.instance;
  }

  async checkAndAwardAchievements(userId: string): Promise<UserAchievement[]> {
    try {
      // Get user's current achievements
      const { data: userAchievements } = await supabase
        .from('user_achievements')
        .select('achievement_id')
        .eq('user_id', userId);

      const earnedAchievementIds = new Set(
        userAchievements?.map(ua => ua.achievement_id) || []
      );

      // Get all achievements
      const { data: allAchievements } = await supabase
        .from('achievements')
        .select('*');

      if (!allAchievements) return [];

      const newAchievements: UserAchievement[] = [];

      for (const achievement of allAchievements) {
        if (earnedAchievementIds.has(achievement.id)) continue;

        const progress = await this.calculateProgress(userId, achievement);
        
        if (progress >= achievement.requirement_value) {
          // Award achievement
          const { data: newAchievement } = await supabase
            .from('user_achievements')
            .insert({
              user_id: userId,
              achievement_id: achievement.id,
              progress: achievement.requirement_value,
            })
            .select('*, achievement:achievements(*)')
            .single();

          if (newAchievement) {
            newAchievements.push(newAchievement);
            
            // Send notification
            await notificationService.sendImmediateNotification(
              `🏆 Achievement Unlocked!`,
              `You've earned "${achievement.name}" - ${achievement.description}`,
              { type: 'achievement', achievementId: achievement.id }
            );
          }
        } else {
          // Update progress
          await supabase
            .from('user_achievements')
            .upsert({
              user_id: userId,
              achievement_id: achievement.id,
              progress,
            });
        }
      }

      return newAchievements;
    } catch (error) {
      console.error('Error checking achievements:', error);
      return [];
    }
  }

  private async calculateProgress(userId: string, achievement: Achievement): Promise<number> {
    try {
      switch (achievement.category) {
        case 'study_streak':
          return await this.calculateStudyStreak(userId);
          
        case 'flashcards':
          const { data: sessions } = await supabase
            .from('study_sessions')
            .select('id')
            .eq('user_id', userId)
            .eq('session_type', 'flashcard_review');
          return sessions?.length || 0;
          
        case 'courses':
          const { data: plans } = await supabase
            .from('study_plans')
            .select('id')
            .eq('user_id', userId)
            .eq('status', 'completed');
          return plans?.length || 0;
          
        case 'time':
          const { data: timeSessions } = await supabase
            .from('study_sessions')
            .select('duration_minutes')
            .eq('user_id', userId);
          const totalMinutes = timeSessions?.reduce((sum, s) => sum + s.duration_minutes, 0) || 0;
          return totalMinutes;
          
        case 'performance':
          const { data: perfSessions } = await supabase
            .from('study_sessions')
            .select('performance_score')
            .eq('user_id', userId)
            .not('performance_score', 'is', null);
          
          if (!perfSessions || perfSessions.length === 0) return 0;
          
          const avgPerformance = perfSessions.reduce(
            (sum, s) => sum + (s.performance_score || 0), 0
          ) / perfSessions.length;
          return Math.round(avgPerformance);
          
        default:
          return 0;
      }
    } catch (error) {
      console.error('Error calculating progress:', error);
      return 0;
    }
  }

  private async calculateStudyStreak(userId: string): Promise<number> {
    try {
      const { data: sessions } = await supabase
        .from('study_sessions')
        .select('completed_at')
        .eq('user_id', userId)
        .order('completed_at', { ascending: false });

      if (!sessions || sessions.length === 0) return 0;

      const today = new Date();
      let streak = 0;

      for (let i = 0; i < 365; i++) {
        const checkDate = new Date(today);
        checkDate.setDate(today.getDate() - i);
        const dateStr = checkDate.toDateString();

        const hasSession = sessions.some(
          session => new Date(session.completed_at).toDateString() === dateStr
        );

        if (hasSession) {
          streak++;
        } else if (i > 0) {
          break;
        }
      }

      return streak;
    } catch (error) {
      console.error('Error calculating study streak:', error);
      return 0;
    }
  }

  async getUserAchievements(userId: string): Promise<UserAchievement[]> {
    try {
      const { data } = await supabase
        .from('user_achievements')
        .select(`
          *,
          achievement:achievements(*)
        `)
        .eq('user_id', userId)
        .order('earned_at', { ascending: false });

      return data || [];
    } catch (error) {
      console.error('Error getting user achievements:', error);
      return [];
    }
  }

  async getUserStats(userId: string): Promise<{
    totalPoints: number;
    achievementsEarned: number;
    totalAchievements: number;
  }> {
    try {
      const [userAchievements, allAchievements] = await Promise.all([
        supabase
          .from('user_achievements')
          .select('achievement:achievements(points)')
          .eq('user_id', userId),
        supabase
          .from('achievements')
          .select('id')
      ]);

      const totalPoints = userAchievements.data?.reduce(
        (sum, ua) => sum + (ua.achievement?.points || 0), 0
      ) || 0;

      return {
        totalPoints,
        achievementsEarned: userAchievements.data?.length || 0,
        totalAchievements: allAchievements.data?.length || 0,
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      return { totalPoints: 0, achievementsEarned: 0, totalAchievements: 0 };
    }
  }

  async createLearningGoal(
    userId: string,
    goal: Omit<LearningGoal, 'id' | 'user_id' | 'current_progress' | 'created_at'>
  ): Promise<LearningGoal | null> {
    try {
      const { data } = await supabase
        .from('learning_goals')
        .insert({
          user_id: userId,
          ...goal,
          current_progress: 0,
        })
        .select()
        .single();

      return data;
    } catch (error) {
      console.error('Error creating learning goal:', error);
      return null;
    }
  }

  async updateGoalProgress(userId: string): Promise<void> {
    try {
      const { data: goals } = await supabase
        .from('learning_goals')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'active');

      if (!goals) return;

      for (const goal of goals) {
        const progress = await this.calculateGoalProgress(userId, goal);
        
        const status = progress >= goal.target_value ? 'completed' : 'active';
        
        await supabase
          .from('learning_goals')
          .update({
            current_progress: progress,
            status,
          })
          .eq('id', goal.id);

        if (status === 'completed') {
          await notificationService.sendImmediateNotification(
            '🎯 Goal Achieved!',
            `Congratulations! You've completed your goal: ${goal.title}`,
            { type: 'goal_completed', goalId: goal.id }
          );
        }
      }
    } catch (error) {
      console.error('Error updating goal progress:', error);
    }
  }

  private async calculateGoalProgress(userId: string, goal: LearningGoal): Promise<number> {
    // Similar to calculateProgress but for goals
    switch (goal.target_type) {
      case 'study_time':
        const { data: sessions } = await supabase
          .from('study_sessions')
          .select('duration_minutes')
          .eq('user_id', userId);
        return sessions?.reduce((sum, s) => sum + s.duration_minutes, 0) || 0;
        
      case 'flashcards':
        const { data: flashcardSessions } = await supabase
          .from('study_sessions')
          .select('id')
          .eq('user_id', userId)
          .eq('session_type', 'flashcard_review');
        return flashcardSessions?.length || 0;
        
      case 'courses':
        const { data: plans } = await supabase
          .from('study_plans')
          .select('id')
          .eq('user_id', userId)
          .eq('status', 'completed');
        return plans?.length || 0;
        
      case 'streak':
        return await this.calculateStudyStreak(userId);
        
      default:
        return 0;
    }
  }

  async getLearningGoals(userId: string): Promise<LearningGoal[]> {
    try {
      const { data } = await supabase
        .from('learning_goals')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      return data || [];
    } catch (error) {
      console.error('Error getting learning goals:', error);
      return [];
    }
  }
}

export const achievementService = AchievementService.getInstance();
