import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import React, { useEffect, useState } from "react";
import {
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";

interface ProgressStats {
  totalStudyTime: number;
  weeklyStudyTime: number;
  monthlyStudyTime: number;
  studyStreak: number;
  totalFlashcardsReviewed: number;
  averagePerformance: number;
  coursesCompleted: number;
  activeCourses: number;
}

interface StudySession {
  id: string;
  duration_minutes: number;
  completed_at: string;
  performance_score: number | null;
  session_type: string;
}

export default function ProgressScreen() {
  const { user } = useUser();
  const [stats, setStats] = useState<ProgressStats>({
    totalStudyTime: 0,
    weeklyStudyTime: 0,
    monthlyStudyTime: 0,
    studyStreak: 0,
    totalFlashcardsReviewed: 0,
    averagePerformance: 0,
    coursesCompleted: 0,
    activeCourses: 0,
  });
  const [recentSessions, setRecentSessions] = useState<StudySession[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadProgressData();
    }
  }, [user]);

  const loadProgressData = async () => {
    try {
      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Load study sessions
      const { data: sessions, error: sessionsError } = await supabase
        .from("study_sessions")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .order("completed_at", { ascending: false });

      if (sessionsError) {
        console.error("Error loading sessions:", sessionsError);
        return;
      }

      const allSessions = sessions || [];
      setRecentSessions(allSessions.slice(0, 10));

      // Calculate stats
      const totalStudyTime = allSessions.reduce(
        (sum, session) => sum + session.duration_minutes,
        0
      );
      const weeklyStudyTime = allSessions
        .filter((session) => new Date(session.completed_at) >= weekAgo)
        .reduce((sum, session) => sum + session.duration_minutes, 0);
      const monthlyStudyTime = allSessions
        .filter((session) => new Date(session.completed_at) >= monthAgo)
        .reduce((sum, session) => sum + session.duration_minutes, 0);

      const flashcardSessions = allSessions.filter(
        (session) => session.session_type === "flashcard_review"
      );
      const totalFlashcardsReviewed = flashcardSessions.length;
      const averagePerformance =
        flashcardSessions.length > 0
          ? flashcardSessions
              .filter((session) => session.performance_score !== null)
              .reduce(
                (sum, session) => sum + (session.performance_score || 0),
                0
              ) / flashcardSessions.length
          : 0;

      // Calculate study streak
      const studyStreak = calculateStudyStreak(allSessions);

      // Load course stats
      const { data: courses } = await supabase
        .from("courses")
        .select("*")
        .eq("user_id", supabaseUser.id);

      const { data: studyPlans } = await supabase
        .from("study_plans")
        .select("*")
        .eq("user_id", supabaseUser.id);

      const coursesCompleted =
        studyPlans?.filter((plan) => plan.status === "completed").length || 0;
      const activeCourses =
        studyPlans?.filter((plan) => plan.status === "active").length || 0;

      setStats({
        totalStudyTime,
        weeklyStudyTime,
        monthlyStudyTime,
        studyStreak,
        totalFlashcardsReviewed,
        averagePerformance,
        coursesCompleted,
        activeCourses,
      });
    } catch (error) {
      console.error("Error loading progress data:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStudyStreak = (sessions: StudySession[]) => {
    const today = new Date();
    let streak = 0;

    for (let i = 0; i < 365; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(today.getDate() - i);
      const dateStr = checkDate.toDateString();

      const hasSession = sessions.some(
        (session) => new Date(session.completed_at).toDateString() === dateStr
      );

      if (hasSession) {
        streak++;
      } else if (i > 0) {
        break;
      }
    }

    return streak;
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProgressData();
    setRefreshing(false);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading progress...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Progress</Text>
        <Text style={styles.subtitle}>Track your learning journey</Text>
      </View>

      <View style={styles.statsGrid}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.studyStreak}</Text>
          <Text style={styles.statLabel}>Day Streak</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {formatDuration(stats.totalStudyTime)}
          </Text>
          <Text style={styles.statLabel}>Total Study Time</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalFlashcardsReviewed}</Text>
          <Text style={styles.statLabel}>Cards Reviewed</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>
            {Math.round(stats.averagePerformance)}%
          </Text>
          <Text style={styles.statLabel}>Avg Performance</Text>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Study Time</Text>
        <View style={styles.timeStats}>
          <View style={styles.timeStatItem}>
            <Text style={styles.timeStatLabel}>This Week</Text>
            <Text style={styles.timeStatValue}>
              {formatDuration(stats.weeklyStudyTime)}
            </Text>
          </View>
          <View style={styles.timeStatItem}>
            <Text style={styles.timeStatLabel}>This Month</Text>
            <Text style={styles.timeStatValue}>
              {formatDuration(stats.monthlyStudyTime)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Course Progress</Text>
        <View style={styles.courseStats}>
          <View style={styles.courseStatItem}>
            <Text style={styles.courseStatNumber}>{stats.activeCourses}</Text>
            <Text style={styles.courseStatLabel}>Active Courses</Text>
          </View>
          <View style={styles.courseStatItem}>
            <Text style={styles.courseStatNumber}>
              {stats.coursesCompleted}
            </Text>
            <Text style={styles.courseStatLabel}>Completed</Text>
          </View>
        </View>
      </View>

      {recentSessions.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Sessions</Text>
          {recentSessions.map((session) => (
            <View key={session.id} style={styles.sessionItem}>
              <View style={styles.sessionInfo}>
                <Text style={styles.sessionType}>
                  {session.session_type.replace("_", " ").toUpperCase()}
                </Text>
                <Text style={styles.sessionDate}>
                  {new Date(session.completed_at).toLocaleDateString()}
                </Text>
              </View>
              <View style={styles.sessionStats}>
                <Text style={styles.sessionDuration}>
                  {formatDuration(session.duration_minutes)}
                </Text>
                {session.performance_score !== null && (
                  <Text style={styles.sessionScore}>
                    {Math.round(session.performance_score)}%
                  </Text>
                )}
              </View>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#007AFF",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#fff",
    opacity: 0.9,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    padding: 20,
    backgroundColor: "#fff",
    marginTop: -10,
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statCard: {
    width: "48%",
    alignItems: "center",
    paddingVertical: 15,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  section: {
    margin: 20,
    marginTop: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
  },
  timeStats: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timeStatItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  timeStatLabel: {
    fontSize: 16,
    color: "#333",
  },
  timeStatValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
  courseStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  courseStatItem: {
    alignItems: "center",
  },
  courseStatNumber: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#007AFF",
    marginBottom: 5,
  },
  courseStatLabel: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  sessionItem: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionType: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 2,
  },
  sessionDate: {
    fontSize: 12,
    color: "#666",
  },
  sessionStats: {
    alignItems: "flex-end",
  },
  sessionDuration: {
    fontSize: 14,
    fontWeight: "600",
    color: "#007AFF",
    marginBottom: 2,
  },
  sessionScore: {
    fontSize: 12,
    color: "#34C759",
  },
});
