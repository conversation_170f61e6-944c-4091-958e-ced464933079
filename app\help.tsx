import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';

export default function HelpScreen() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Help & Support</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>Frequently Asked Questions</Text>

        <View style={styles.faqItem}>
          <Text style={styles.question}>How do I upload a course?</Text>
          <Text style={styles.answer}>
            Tap the "Upload Course" button on your dashboard or in the courses tab. Select a PDF, DOC, or TXT file containing your syllabus or course material. Our AI will analyze it and create a personalized study plan.
          </Text>
        </View>

        <View style={styles.faqItem}>
          <Text style={styles.question}>How does spaced repetition work?</Text>
          <Text style={styles.answer}>
            Our spaced repetition algorithm shows you flashcards at optimal intervals based on how well you know each card. Cards you find difficult appear more frequently, while easy cards appear less often.
          </Text>
        </View>

        <View style={styles.faqItem}>
          <Text style={styles.question}>Can I edit generated flashcards?</Text>
          <Text style={styles.answer}>
            Yes! While we automatically generate flashcards from your course material, you can edit, delete, or add new flashcards at any time to customize your learning experience.
          </Text>
        </View>

        <View style={styles.faqItem}>
          <Text style={styles.question}>What file formats are supported?</Text>
          <Text style={styles.answer}>
            We support PDF, DOC, DOCX, and TXT files. For best results, use text-based documents rather than image-heavy files.
          </Text>
        </View>

        <View style={styles.faqItem}>
          <Text style={styles.question}>How do I cancel my subscription?</Text>
          <Text style={styles.answer}>
            Go to Profile → Subscription → Manage Subscription. You can cancel anytime and will retain access until your current billing period ends.
          </Text>
        </View>

        <View style={styles.contactSection}>
          <Text style={styles.contactTitle}>Still need help?</Text>
          <Text style={styles.contactText}>
            Contact our support <NAME_EMAIL>
          </Text>
          <Text style={styles.contactText}>
            We typically respond within 24 hours.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 30,
  },
  faqItem: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  question: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  answer: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  contactSection: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginTop: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  contactTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  contactText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 5,
  },
});
