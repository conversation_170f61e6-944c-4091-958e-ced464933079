import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import React, { useEffect, useState } from "react";
import {
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from "react-native";

interface ProgressStats {
  totalStudyTime: number;
  weeklyStudyTime: number;
  monthlyStudyTime: number;
  studyStreak: number;
  totalFlashcardsReviewed: number;
  averagePerformance: number;
  coursesCompleted: number;
  activeCourses: number;
}

interface StudySession {
  id: string;
  duration_minutes: number;
  completed_at: string;
  performance_score: number | null;
  session_type: string;
}

interface WeeklyData {
  week: string;
  studyTime: number;
  sessions: number;
  performance: number;
}

interface CategoryPerformance {
  category: string;
  totalCards: number;
  masteredCards: number;
  averageScore: number;
  needsReview: number;
}

interface LearningInsight {
  type: "strength" | "weakness" | "recommendation";
  title: string;
  description: string;
  actionable: boolean;
}

export default function ProgressScreen() {
  const { user } = useUser();
  const [stats, setStats] = useState<ProgressStats>({
    totalStudyTime: 0,
    weeklyStudyTime: 0,
    monthlyStudyTime: 0,
    studyStreak: 0,
    totalFlashcardsReviewed: 0,
    averagePerformance: 0,
    coursesCompleted: 0,
    activeCourses: 0,
  });
  const [recentSessions, setRecentSessions] = useState<StudySession[]>([]);
  const [weeklyData, setWeeklyData] = useState<WeeklyData[]>([]);
  const [categoryPerformance, setCategoryPerformance] = useState<
    CategoryPerformance[]
  >([]);
  const [insights, setInsights] = useState<LearningInsight[]>([]);
  const [activeTab, setActiveTab] = useState<
    "overview" | "trends" | "insights"
  >("overview");
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadProgressData();
    }
  }, [user]);

  const loadProgressData = async () => {
    try {
      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const now = new Date();
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      // Load study sessions
      const { data: sessions, error: sessionsError } = await supabase
        .from("study_sessions")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .order("completed_at", { ascending: false });

      if (sessionsError) {
        console.error("Error loading sessions:", sessionsError);
        return;
      }

      const allSessions = sessions || [];
      setRecentSessions(allSessions.slice(0, 10));

      // Calculate stats
      const totalStudyTime = allSessions.reduce(
        (sum, session) => sum + session.duration_minutes,
        0
      );
      const weeklyStudyTime = allSessions
        .filter((session) => new Date(session.completed_at) >= weekAgo)
        .reduce((sum, session) => sum + session.duration_minutes, 0);
      const monthlyStudyTime = allSessions
        .filter((session) => new Date(session.completed_at) >= monthAgo)
        .reduce((sum, session) => sum + session.duration_minutes, 0);

      const flashcardSessions = allSessions.filter(
        (session) => session.session_type === "flashcard_review"
      );
      const totalFlashcardsReviewed = flashcardSessions.length;
      const averagePerformance =
        flashcardSessions.length > 0
          ? flashcardSessions
              .filter((session) => session.performance_score !== null)
              .reduce(
                (sum, session) => sum + (session.performance_score || 0),
                0
              ) / flashcardSessions.length
          : 0;

      // Calculate study streak
      const studyStreak = calculateStudyStreak(allSessions);

      // Load course stats
      const { data: courses } = await supabase
        .from("courses")
        .select("*")
        .eq("user_id", supabaseUser.id);

      const { data: studyPlans } = await supabase
        .from("study_plans")
        .select("*")
        .eq("user_id", supabaseUser.id);

      const coursesCompleted =
        studyPlans?.filter((plan) => plan.status === "completed").length || 0;
      const activeCourses =
        studyPlans?.filter((plan) => plan.status === "active").length || 0;

      setStats({
        totalStudyTime,
        weeklyStudyTime,
        monthlyStudyTime,
        studyStreak,
        totalFlashcardsReviewed,
        averagePerformance,
        coursesCompleted,
        activeCourses,
      });

      // Load enhanced analytics
      await loadWeeklyTrends(supabaseUser.id, allSessions);
      await loadCategoryPerformance(supabaseUser.id);
      await generateInsights(allSessions, {
        totalStudyTime,
        weeklyStudyTime,
        monthlyStudyTime,
        studyStreak,
        totalFlashcardsReviewed,
        averagePerformance,
        coursesCompleted,
        activeCourses,
      });
    } catch (error) {
      console.error("Error loading progress data:", error);
    } finally {
      setLoading(false);
    }
  };

  const calculateStudyStreak = (sessions: StudySession[]) => {
    const today = new Date();
    let streak = 0;

    for (let i = 0; i < 365; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(today.getDate() - i);
      const dateStr = checkDate.toDateString();

      const hasSession = sessions.some(
        (session) => new Date(session.completed_at).toDateString() === dateStr
      );

      if (hasSession) {
        streak++;
      } else if (i > 0) {
        break;
      }
    }

    return streak;
  };

  const loadWeeklyTrends = async (userId: string, sessions: StudySession[]) => {
    const weeks: WeeklyData[] = [];
    const now = new Date();

    for (let i = 7; i >= 0; i--) {
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - i * 7);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);

      const weekSessions = sessions.filter((session) => {
        const sessionDate = new Date(session.completed_at);
        return sessionDate >= weekStart && sessionDate <= weekEnd;
      });

      const studyTime = weekSessions.reduce(
        (sum, session) => sum + session.duration_minutes,
        0
      );
      const performance =
        weekSessions.length > 0
          ? weekSessions
              .filter((s) => s.performance_score !== null)
              .reduce((sum, s) => sum + (s.performance_score || 0), 0) /
            weekSessions.length
          : 0;

      weeks.push({
        week: `${weekStart.getMonth() + 1}/${weekStart.getDate()}`,
        studyTime,
        sessions: weekSessions.length,
        performance: Math.round(performance),
      });
    }

    setWeeklyData(weeks);
  };

  const loadCategoryPerformance = async (userId: string) => {
    try {
      const { data: flashcards } = await supabase
        .from("flashcards")
        .select("category, difficulty, review_count, ease_factor")
        .eq("user_id", userId);

      if (!flashcards) return;

      const categoryMap = new Map<string, CategoryPerformance>();

      flashcards.forEach((card) => {
        const category = card.category || "Uncategorized";

        if (!categoryMap.has(category)) {
          categoryMap.set(category, {
            category,
            totalCards: 0,
            masteredCards: 0,
            averageScore: 0,
            needsReview: 0,
          });
        }

        const categoryData = categoryMap.get(category)!;
        categoryData.totalCards++;

        // Consider a card mastered if it has been reviewed multiple times with high ease factor
        if (card.review_count >= 3 && card.ease_factor >= 2.5) {
          categoryData.masteredCards++;
        }

        // Cards that need review (low ease factor or few reviews)
        if (card.ease_factor < 2.0 || card.review_count < 2) {
          categoryData.needsReview++;
        }

        // Calculate average score based on ease factor (simplified)
        categoryData.averageScore += (card.ease_factor - 1.3) * 50; // Convert ease factor to percentage
      });

      const categories = Array.from(categoryMap.values()).map((cat) => ({
        ...cat,
        averageScore: Math.round(cat.averageScore / cat.totalCards),
      }));

      setCategoryPerformance(categories);
    } catch (error) {
      console.error("Error loading category performance:", error);
    }
  };

  const generateInsights = async (
    sessions: StudySession[],
    stats: ProgressStats
  ) => {
    const insights: LearningInsight[] = [];

    // Study consistency insights
    if (stats.studyStreak >= 7) {
      insights.push({
        type: "strength",
        title: "Great Study Consistency!",
        description: `You've maintained a ${stats.studyStreak}-day study streak. Keep up the excellent work!`,
        actionable: false,
      });
    } else if (stats.studyStreak < 3) {
      insights.push({
        type: "weakness",
        title: "Improve Study Consistency",
        description:
          "Try to study a little bit every day to build a strong learning habit.",
        actionable: true,
      });
    }

    // Performance insights
    if (stats.averagePerformance >= 80) {
      insights.push({
        type: "strength",
        title: "Excellent Performance",
        description: `Your average performance of ${Math.round(
          stats.averagePerformance
        )}% shows strong understanding.`,
        actionable: false,
      });
    } else if (stats.averagePerformance < 60) {
      insights.push({
        type: "weakness",
        title: "Focus on Weak Areas",
        description:
          "Consider reviewing difficult flashcards more frequently to improve retention.",
        actionable: true,
      });
    }

    // Study time insights
    const avgDailyTime = stats.weeklyStudyTime / 7;
    if (avgDailyTime < 15) {
      insights.push({
        type: "recommendation",
        title: "Increase Study Time",
        description:
          "Try to study for at least 15-20 minutes daily for better retention.",
        actionable: true,
      });
    }

    // Recent activity insights
    const recentSessions = sessions.slice(0, 7);
    if (recentSessions.length === 0) {
      insights.push({
        type: "weakness",
        title: "No Recent Activity",
        description:
          "You haven't studied recently. Get back on track with a quick review session!",
        actionable: true,
      });
    }

    setInsights(insights);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProgressData();
    setRefreshing(false);
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const renderSimpleChart = (data: WeeklyData[]) => {
    const maxStudyTime = Math.max(...data.map((d) => d.studyTime));
    const screenWidth = Dimensions.get("window").width - 40;
    const chartWidth = screenWidth - 40;
    const barWidth = chartWidth / data.length - 10;

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Weekly Study Time (minutes)</Text>
        <View style={styles.chart}>
          {data.map((week, index) => {
            const height =
              maxStudyTime > 0 ? (week.studyTime / maxStudyTime) * 100 : 0;
            return (
              <View key={index} style={styles.chartBar}>
                <View
                  style={[
                    styles.bar,
                    {
                      height: Math.max(height, 2),
                      width: barWidth,
                      backgroundColor:
                        week.studyTime > 0 ? "#007AFF" : "#e0e0e0",
                    },
                  ]}
                />
                <Text style={styles.chartLabel}>{week.week}</Text>
                <Text style={styles.chartValue}>{week.studyTime}m</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const getInsightIcon = (type: LearningInsight["type"]) => {
    switch (type) {
      case "strength":
        return "💪";
      case "weakness":
        return "⚠️";
      case "recommendation":
        return "💡";
      default:
        return "📊";
    }
  };

  const getInsightColor = (type: LearningInsight["type"]) => {
    switch (type) {
      case "strength":
        return "#34C759";
      case "weakness":
        return "#FF3B30";
      case "recommendation":
        return "#007AFF";
      default:
        return "#666";
    }
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading progress...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Your Progress</Text>
        <Text style={styles.subtitle}>Track your learning journey</Text>
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "overview" && styles.activeTab]}
          onPress={() => setActiveTab("overview")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "overview" && styles.activeTabText,
            ]}
          >
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "trends" && styles.activeTab]}
          onPress={() => setActiveTab("trends")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "trends" && styles.activeTabText,
            ]}
          >
            Trends
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "insights" && styles.activeTab]}
          onPress={() => setActiveTab("insights")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "insights" && styles.activeTabText,
            ]}
          >
            Insights
          </Text>
        </TouchableOpacity>
      </View>

      {activeTab === "overview" && (
        <>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{stats.studyStreak}</Text>
              <Text style={styles.statLabel}>Day Streak</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>
                {formatDuration(stats.totalStudyTime)}
              </Text>
              <Text style={styles.statLabel}>Total Study Time</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>
                {stats.totalFlashcardsReviewed}
              </Text>
              <Text style={styles.statLabel}>Cards Reviewed</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>
                {Math.round(stats.averagePerformance)}%
              </Text>
              <Text style={styles.statLabel}>Avg Performance</Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Study Time</Text>
            <View style={styles.timeStats}>
              <View style={styles.timeStatItem}>
                <Text style={styles.timeStatLabel}>This Week</Text>
                <Text style={styles.timeStatValue}>
                  {formatDuration(stats.weeklyStudyTime)}
                </Text>
              </View>
              <View style={styles.timeStatItem}>
                <Text style={styles.timeStatLabel}>This Month</Text>
                <Text style={styles.timeStatValue}>
                  {formatDuration(stats.monthlyStudyTime)}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Course Progress</Text>
            <View style={styles.courseStats}>
              <View style={styles.courseStatItem}>
                <Text style={styles.courseStatNumber}>
                  {stats.activeCourses}
                </Text>
                <Text style={styles.courseStatLabel}>Active Courses</Text>
              </View>
              <View style={styles.courseStatItem}>
                <Text style={styles.courseStatNumber}>
                  {stats.coursesCompleted}
                </Text>
                <Text style={styles.courseStatLabel}>Completed</Text>
              </View>
            </View>
          </View>
        </>
      )}

      {activeTab === "trends" && (
        <>
          {weeklyData.length > 0 && renderSimpleChart(weeklyData)}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Category Performance</Text>
            {categoryPerformance.map((category, index) => (
              <View key={index} style={styles.categoryItem}>
                <View style={styles.categoryHeader}>
                  <Text style={styles.categoryName}>{category.category}</Text>
                  <Text style={styles.categoryScore}>
                    {category.averageScore}%
                  </Text>
                </View>
                <View style={styles.categoryStats}>
                  <Text style={styles.categoryStatText}>
                    {category.masteredCards}/{category.totalCards} mastered
                  </Text>
                  <Text style={styles.categoryStatText}>
                    {category.needsReview} need review
                  </Text>
                </View>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: `${
                          (category.masteredCards / category.totalCards) * 100
                        }%`,
                      },
                    ]}
                  />
                </View>
              </View>
            ))}
          </View>

          {recentSessions.length > 0 && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Recent Sessions</Text>
              {recentSessions.slice(0, 5).map((session) => (
                <View key={session.id} style={styles.sessionItem}>
                  <View style={styles.sessionInfo}>
                    <Text style={styles.sessionType}>
                      {session.session_type.replace("_", " ").toUpperCase()}
                    </Text>
                    <Text style={styles.sessionDate}>
                      {new Date(session.completed_at).toLocaleDateString()}
                    </Text>
                  </View>
                  <View style={styles.sessionStats}>
                    <Text style={styles.sessionDuration}>
                      {formatDuration(session.duration_minutes)}
                    </Text>
                    {session.performance_score !== null && (
                      <Text style={styles.sessionScore}>
                        {Math.round(session.performance_score)}%
                      </Text>
                    )}
                  </View>
                </View>
              ))}
            </View>
          )}
        </>
      )}

      {activeTab === "insights" && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Learning Insights</Text>
          {insights.length === 0 ? (
            <View style={styles.noInsights}>
              <Text style={styles.noInsightsText}>
                Keep studying to unlock personalized insights!
              </Text>
            </View>
          ) : (
            insights.map((insight, index) => (
              <View key={index} style={styles.insightItem}>
                <View style={styles.insightHeader}>
                  <Text style={styles.insightIcon}>
                    {getInsightIcon(insight.type)}
                  </Text>
                  <Text
                    style={[
                      styles.insightTitle,
                      { color: getInsightColor(insight.type) },
                    ]}
                  >
                    {insight.title}
                  </Text>
                </View>
                <Text style={styles.insightDescription}>
                  {insight.description}
                </Text>
                {insight.actionable && (
                  <View style={styles.actionableBadge}>
                    <Text style={styles.actionableText}>Actionable</Text>
                  </View>
                )}
              </View>
            ))
          )}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#007AFF",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#fff",
    opacity: 0.9,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  statsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    padding: 20,
    backgroundColor: "#fff",
    marginTop: -10,
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statCard: {
    width: "48%",
    alignItems: "center",
    paddingVertical: 15,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  section: {
    margin: 20,
    marginTop: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
  },
  timeStats: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timeStatItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  timeStatLabel: {
    fontSize: 16,
    color: "#333",
  },
  timeStatValue: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
  courseStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  courseStatItem: {
    alignItems: "center",
  },
  courseStatNumber: {
    fontSize: 32,
    fontWeight: "bold",
    color: "#007AFF",
    marginBottom: 5,
  },
  courseStatLabel: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  sessionItem: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sessionInfo: {
    flex: 1,
  },
  sessionType: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 2,
  },
  sessionDate: {
    fontSize: 12,
    color: "#666",
  },
  sessionStats: {
    alignItems: "flex-end",
  },
  sessionDuration: {
    fontSize: 14,
    fontWeight: "600",
    color: "#007AFF",
    marginBottom: 2,
  },
  sessionScore: {
    fontSize: 12,
    color: "#34C759",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    marginHorizontal: 20,
    marginTop: 10,
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTab: {
    borderBottomColor: "#007AFF",
  },
  tabText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  activeTabText: {
    color: "#007AFF",
    fontWeight: "600",
  },
  chartContainer: {
    backgroundColor: "#fff",
    margin: 20,
    borderRadius: 12,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 15,
    textAlign: "center",
  },
  chart: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "flex-end",
    height: 120,
    marginBottom: 10,
  },
  chartBar: {
    alignItems: "center",
    flex: 1,
  },
  bar: {
    backgroundColor: "#007AFF",
    borderRadius: 2,
    marginBottom: 5,
  },
  chartLabel: {
    fontSize: 10,
    color: "#666",
    marginBottom: 2,
  },
  chartValue: {
    fontSize: 10,
    color: "#333",
    fontWeight: "600",
  },
  categoryItem: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  categoryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  categoryScore: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#007AFF",
  },
  categoryStats: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  categoryStatText: {
    fontSize: 12,
    color: "#666",
  },
  progressBar: {
    height: 4,
    backgroundColor: "#e0e0e0",
    borderRadius: 2,
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#34C759",
    borderRadius: 2,
  },
  insightItem: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  insightHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  insightIcon: {
    fontSize: 20,
    marginRight: 10,
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  insightDescription: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
    marginBottom: 8,
  },
  actionableBadge: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: "flex-start",
  },
  actionableText: {
    fontSize: 10,
    color: "#fff",
    fontWeight: "600",
  },
  noInsights: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 30,
    alignItems: "center",
  },
  noInsightsText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
  },
});
