import { GoogleGenerativeAI } from '@google/generative-ai';

const apiKey = process.env.EXPO_PUBLIC_GEMINI_API_KEY!;

if (!apiKey) {
  throw new Error('Missing Gemini API key. Please set EXPO_PUBLIC_GEMINI_API_KEY in your .env');
}

const genAI = new GoogleGenerativeAI(apiKey);

export interface CourseAnalysis {
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedWeeks: number;
  topics: string[];
  learningObjectives: string[];
}

export interface StudyPlan {
  title: string;
  description: string;
  milestones: {
    week: number;
    title: string;
    description: string;
    topics: string[];
    estimatedHours: number;
  }[];
  totalWeeks: number;
}

export interface FlashcardSet {
  category: string;
  cards: {
    front: string;
    back: string;
    difficulty: 'easy' | 'medium' | 'hard';
  }[];
}

export class GeminiService {
  private model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

  async analyzeCourse(syllabusContent: string): Promise<CourseAnalysis> {
    const prompt = `
      Analyze the following course syllabus and extract key information:

      ${syllabusContent}

      Please provide a JSON response with the following structure:
      {
        "title": "Course title",
        "description": "Brief course description (2-3 sentences)",
        "difficulty": "beginner|intermediate|advanced",
        "estimatedWeeks": number,
        "topics": ["topic1", "topic2", ...],
        "learningObjectives": ["objective1", "objective2", ...]
      }

      Make sure the response is valid JSON only, no additional text.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Clean the response to ensure it's valid JSON
      const cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();
      
      return JSON.parse(cleanedText);
    } catch (error) {
      console.error('Error analyzing course:', error);
      throw new Error('Failed to analyze course content');
    }
  }

  async generateStudyPlan(courseAnalysis: CourseAnalysis): Promise<StudyPlan> {
    const prompt = `
      Create a detailed study plan for the following course:

      Title: ${courseAnalysis.title}
      Description: ${courseAnalysis.description}
      Difficulty: ${courseAnalysis.difficulty}
      Duration: ${courseAnalysis.estimatedWeeks} weeks
      Topics: ${courseAnalysis.topics.join(', ')}
      Learning Objectives: ${courseAnalysis.learningObjectives.join(', ')}

      Please provide a JSON response with the following structure:
      {
        "title": "Study plan title",
        "description": "Study plan description",
        "milestones": [
          {
            "week": 1,
            "title": "Week title",
            "description": "What to focus on this week",
            "topics": ["topic1", "topic2"],
            "estimatedHours": number
          }
        ],
        "totalWeeks": number
      }

      Create a progressive learning plan that builds knowledge week by week.
      Make sure the response is valid JSON only, no additional text.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      const cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();
      
      return JSON.parse(cleanedText);
    } catch (error) {
      console.error('Error generating study plan:', error);
      throw new Error('Failed to generate study plan');
    }
  }

  async generateFlashcards(courseAnalysis: CourseAnalysis, maxCards: number = 20): Promise<FlashcardSet[]> {
    const prompt = `
      Create flashcards for the following course:

      Title: ${courseAnalysis.title}
      Topics: ${courseAnalysis.topics.join(', ')}
      Learning Objectives: ${courseAnalysis.learningObjectives.join(', ')}

      Please create ${maxCards} flashcards organized by topic/category.
      
      Provide a JSON response with the following structure:
      [
        {
          "category": "Topic/Category name",
          "cards": [
            {
              "front": "Question or term",
              "back": "Answer or definition",
              "difficulty": "easy|medium|hard"
            }
          ]
        }
      ]

      Make the flashcards educational and progressive in difficulty.
      Make sure the response is valid JSON only, no additional text.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      const cleanedText = text.replace(/```json\n?|\n?```/g, '').trim();
      
      return JSON.parse(cleanedText);
    } catch (error) {
      console.error('Error generating flashcards:', error);
      throw new Error('Failed to generate flashcards');
    }
  }

  async extractTextFromDocument(documentContent: string, fileType: string): Promise<string> {
    const prompt = `
      Extract and clean the text content from the following document:

      File Type: ${fileType}
      Content: ${documentContent}

      Please extract only the readable text content, removing any formatting, headers, footers, 
      page numbers, or irrelevant metadata. Focus on the main educational content.
      
      Return only the cleaned text content, no additional formatting or explanations.
    `;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text().trim();
    } catch (error) {
      console.error('Error extracting text:', error);
      throw new Error('Failed to extract text from document');
    }
  }
}

export const geminiService = new GeminiService();
