import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";

interface Course {
  id: string;
  title: string;
}

export default function CreateFlashcardScreen() {
  const { user } = useUser();
  const [front, setFront] = useState("");
  const [back, setBack] = useState("");
  const [category, setCategory] = useState("");
  const [difficulty, setDifficulty] = useState<"easy" | "medium" | "hard">(
    "medium"
  );
  const [selectedCourse, setSelectedCourse] = useState<string | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      loadCourses();
    }
  }, [user]);

  const loadCourses = async () => {
    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      const { data, error } = await supabase
        .from("courses")
        .select("id, title")
        .eq("user_id", supabaseUser.id)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error loading courses:", error);
        return;
      }

      setCourses(data || []);
      if (data && data.length > 0) {
        setSelectedCourse(data[0].id);
      }
    } catch (error) {
      console.error("Error loading courses:", error);
    }
  };

  const createFlashcard = async () => {
    if (!front.trim() || !back.trim()) {
      Alert.alert(
        "Error",
        "Please fill in both front and back of the flashcard"
      );
      return;
    }

    if (!selectedCourse) {
      Alert.alert("Error", "Please select a course for this flashcard");
      return;
    }

    setLoading(true);

    try {
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) {
        throw new Error("User not found");
      }

      const { error } = await supabase.from("flashcards").insert({
        user_id: supabaseUser.id,
        course_id: selectedCourse,
        front: front.trim(),
        back: back.trim(),
        category: category.trim() || "Custom",
        difficulty,
        next_review_date: new Date().toISOString(),
        review_count: 0,
        ease_factor: 2.5,
        interval_days: 1,
      });

      if (error) throw error;

      Alert.alert("Success", "Flashcard created successfully!", [
        {
          text: "Create Another",
          onPress: () => {
            setFront("");
            setBack("");
            setCategory("");
            setDifficulty("medium");
          },
        },
        {
          text: "Done",
          onPress: () => router.back(),
        },
      ]);
    } catch (error) {
      console.error("Error creating flashcard:", error);
      Alert.alert("Error", "Failed to create flashcard");
    } finally {
      setLoading(false);
    }
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case "easy":
        return "#34C759";
      case "medium":
        return "#FF9500";
      case "hard":
        return "#FF3B30";
      default:
        return "#007AFF";
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Flashcard</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.title}>Create Custom Flashcard</Text>
        <Text style={styles.subtitle}>
          Add your own flashcard to enhance your learning
        </Text>

        <View style={styles.formSection}>
          <Text style={styles.label}>Front (Question/Term)</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter the question or term..."
            value={front}
            onChangeText={setFront}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Back (Answer/Definition)</Text>
          <TextInput
            style={styles.textInput}
            placeholder="Enter the answer or definition..."
            value={back}
            onChangeText={setBack}
            multiline
            numberOfLines={3}
          />
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Category (Optional)</Text>
          <TextInput
            style={styles.singleLineInput}
            placeholder="e.g., Vocabulary, Concepts, Formulas..."
            value={category}
            onChangeText={setCategory}
          />
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Difficulty</Text>
          <View style={styles.difficultyContainer}>
            {(["easy", "medium", "hard"] as const).map((level) => (
              <TouchableOpacity
                key={level}
                style={[
                  styles.difficultyButton,
                  difficulty === level && styles.selectedDifficulty,
                  { borderColor: getDifficultyColor(level) },
                  difficulty === level && {
                    backgroundColor: getDifficultyColor(level),
                  },
                ]}
                onPress={() => setDifficulty(level)}
              >
                <Text
                  style={[
                    styles.difficultyText,
                    difficulty === level && styles.selectedDifficultyText,
                  ]}
                >
                  {level.charAt(0).toUpperCase() + level.slice(1)}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.formSection}>
          <Text style={styles.label}>Course</Text>
          {courses.length === 0 ? (
            <View style={styles.noCourses}>
              <Text style={styles.noCoursesText}>
                No courses available. Upload a course first.
              </Text>
              <TouchableOpacity
                style={styles.uploadCourseButton}
                onPress={() => router.push("/upload-course")}
              >
                <Text style={styles.uploadCourseButtonText}>Upload Course</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.courseContainer}>
              {courses.map((course) => (
                <TouchableOpacity
                  key={course.id}
                  style={[
                    styles.courseOption,
                    selectedCourse === course.id && styles.selectedCourse,
                  ]}
                  onPress={() => setSelectedCourse(course.id)}
                >
                  <Text
                    style={[
                      styles.courseText,
                      selectedCourse === course.id && styles.selectedCourseText,
                    ]}
                  >
                    {course.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        <TouchableOpacity
          style={[styles.createButton, loading && styles.createButtonDisabled]}
          onPress={createFlashcard}
          disabled={loading || courses.length === 0}
        >
          <Text style={styles.createButtonText}>
            {loading ? "Creating..." : "Create Flashcard"}
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  backButton: {
    padding: 5,
  },
  backButtonText: {
    fontSize: 16,
    color: "#007AFF",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  placeholder: {
    width: 50,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    marginBottom: 30,
  },
  formSection: {
    marginBottom: 25,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    color: "#333",
    backgroundColor: "#fff",
    textAlignVertical: "top",
    minHeight: 80,
  },
  singleLineInput: {
    borderWidth: 1,
    borderColor: "#e0e0e0",
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    color: "#333",
    backgroundColor: "#fff",
    height: 50,
  },
  difficultyContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  difficultyButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderWidth: 2,
    borderRadius: 8,
    marginHorizontal: 5,
    alignItems: "center",
  },
  selectedDifficulty: {
    backgroundColor: "#007AFF",
  },
  difficultyText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
  },
  selectedDifficultyText: {
    color: "#fff",
  },
  courseContainer: {
    backgroundColor: "#fff",
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  courseOption: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  selectedCourse: {
    backgroundColor: "#007AFF",
  },
  courseText: {
    fontSize: 16,
    color: "#333",
  },
  selectedCourseText: {
    color: "#fff",
    fontWeight: "600",
  },
  noCourses: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 20,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  noCoursesText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 15,
  },
  uploadCourseButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  uploadCourseButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  createButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 20,
    marginBottom: 40,
  },
  createButtonDisabled: {
    backgroundColor: "#ccc",
  },
  createButtonText: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
  },
});
