import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface Flashcard {
  id: string;
  front: string;
  back: string;
  category: string;
  difficulty: "easy" | "medium" | "hard";
  next_review_date: string;
  review_count: number;
  course_id: string;
}

export default function FlashcardsScreen() {
  const { user } = useUser();
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [dueCards, setDueCards] = useState<Flashcard[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"all" | "due">("due");

  useEffect(() => {
    if (user) {
      loadFlashcards();
    }
  }, [user]);

  const loadFlashcards = async () => {
    try {
      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      // Load all flashcards
      const { data: allCards, error: allError } = await supabase
        .from("flashcards")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .order("created_at", { ascending: false });

      if (allError) {
        console.error("Error loading flashcards:", allError);
        return;
      }

      setFlashcards(allCards || []);

      // Load due flashcards
      const { data: dueCardsData, error: dueError } = await supabase
        .from("flashcards")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .lte("next_review_date", new Date().toISOString())
        .order("next_review_date", { ascending: true });

      if (dueError) {
        console.error("Error loading due flashcards:", dueError);
        return;
      }

      setDueCards(dueCardsData || []);
    } catch (error) {
      console.error("Error loading flashcards:", error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFlashcards();
    setRefreshing(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "#34C759";
      case "medium":
        return "#FF9500";
      case "hard":
        return "#FF3B30";
      default:
        return "#007AFF";
    }
  };

  const renderFlashcardItem = ({ item }: { item: Flashcard }) => (
    <TouchableOpacity
      style={styles.flashcardCard}
      onPress={() => router.push(`/flashcard/${item.id}`)}
    >
      <View style={styles.cardHeader}>
        <View style={styles.cardInfo}>
          {item.category && (
            <Text style={styles.category}>{item.category}</Text>
          )}
          <View
            style={[
              styles.difficultyBadge,
              { backgroundColor: getDifficultyColor(item.difficulty) },
            ]}
          >
            <Text style={styles.difficultyText}>
              {item.difficulty.toUpperCase()}
            </Text>
          </View>
        </View>
        <Text style={styles.reviewCount}>
          Reviewed {item.review_count} times
        </Text>
      </View>

      <Text style={styles.cardFront} numberOfLines={2}>
        {item.front}
      </Text>

      <View style={styles.cardFooter}>
        <Text style={styles.nextReview}>
          Next review: {new Date(item.next_review_date).toLocaleDateString()}
        </Text>
        {new Date(item.next_review_date) <= new Date() && (
          <View style={styles.dueBadge}>
            <Text style={styles.dueText}>DUE</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading flashcards...</Text>
      </View>
    );
  }

  const currentData = activeTab === "all" ? flashcards : dueCards;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Flashcards</Text>
        {dueCards.length > 0 && (
          <TouchableOpacity
            style={styles.reviewButton}
            onPress={() => router.push("/flashcard-review")}
          >
            <Text style={styles.reviewButtonText}>Review Now</Text>
          </TouchableOpacity>
        )}
      </View>

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "due" && styles.activeTab]}
          onPress={() => setActiveTab("due")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "due" && styles.activeTabText,
            ]}
          >
            Due ({dueCards.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "all" && styles.activeTab]}
          onPress={() => setActiveTab("all")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "all" && styles.activeTabText,
            ]}
          >
            All ({flashcards.length})
          </Text>
        </TouchableOpacity>
      </View>

      {currentData.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>
            {activeTab === "due"
              ? "No cards due for review"
              : "No flashcards yet"}
          </Text>
          <Text style={styles.emptyDescription}>
            {activeTab === "due"
              ? "Great job! All your flashcards are up to date."
              : "Upload a course to automatically generate flashcards or create them manually."}
          </Text>
          {activeTab === "all" && (
            <TouchableOpacity
              style={styles.uploadButton}
              onPress={() => router.push("/upload-course")}
            >
              <Text style={styles.uploadButtonText}>Upload Course</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={currentData}
          renderItem={renderFlashcardItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  reviewButton: {
    backgroundColor: "#34C759",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  reviewButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTab: {
    borderBottomColor: "#007AFF",
  },
  tabText: {
    fontSize: 16,
    color: "#666",
  },
  activeTabText: {
    color: "#007AFF",
    fontWeight: "600",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 10,
    textAlign: "center",
  },
  emptyDescription: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 30,
    lineHeight: 24,
  },
  uploadButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  uploadButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  listContainer: {
    padding: 20,
  },
  flashcardCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 10,
  },
  cardInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  category: {
    fontSize: 12,
    color: "#666",
    marginRight: 10,
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "bold",
  },
  reviewCount: {
    fontSize: 12,
    color: "#999",
  },
  cardFront: {
    fontSize: 16,
    color: "#333",
    lineHeight: 22,
    marginBottom: 15,
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  nextReview: {
    fontSize: 12,
    color: "#666",
  },
  dueBadge: {
    backgroundColor: "#FF3B30",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  dueText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "bold",
  },
});
