import { geminiService } from "@/lib/gemini";
import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import * as DocumentPicker from "expo-document-picker";
import * as FileSystem from "expo-file-system";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

export default function UploadCourseScreen() {
  const { user } = useUser();
  const [selectedFile, setSelectedFile] =
    useState<DocumentPicker.DocumentPickerResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStep, setProcessingStep] = useState("");

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: [
          "application/pdf",
          "text/plain",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ],
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedFile(result);
      }
    } catch (error) {
      console.error("Error picking document:", error);
      Alert.alert("Error", "Failed to pick document");
    }
  };

  const readFileContent = async (uri: string): Promise<string> => {
    try {
      const content = await FileSystem.readAsStringAsync(uri);
      return content;
    } catch (error) {
      console.error("Error reading file:", error);
      throw new Error("Failed to read file content");
    }
  };

  const processCourse = async () => {
    if (
      !selectedFile ||
      !selectedFile.assets ||
      selectedFile.assets.length === 0
    ) {
      Alert.alert("Error", "Please select a file first");
      return;
    }

    if (!user) {
      Alert.alert("Error", "Please sign in to upload courses");
      return;
    }

    setIsProcessing(true);

    try {
      const file = selectedFile.assets[0];

      setProcessingStep("Reading file content...");
      const fileContent = await readFileContent(file.uri);

      setProcessingStep("Extracting text with AI...");
      const extractedText = await geminiService.extractTextFromDocument(
        fileContent,
        file.mimeType || "text/plain"
      );

      setProcessingStep("Analyzing course content...");
      const courseAnalysis = await geminiService.analyzeCourse(extractedText);

      setProcessingStep("Saving course to database...");

      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user.id)
        .single();

      if (!supabaseUser) {
        throw new Error("User not found in database");
      }

      // Save course to database
      const { data: course, error: courseError } = await supabase
        .from("courses")
        .insert({
          user_id: supabaseUser.id,
          title: courseAnalysis.title,
          description: courseAnalysis.description,
          syllabus_content: extractedText,
          difficulty_level: courseAnalysis.difficulty,
          estimated_duration_weeks: courseAnalysis.estimatedWeeks,
        })
        .select()
        .single();

      if (courseError) {
        throw courseError;
      }

      setProcessingStep("Generating study plan...");
      const studyPlan = await geminiService.generateStudyPlan(courseAnalysis);

      // Save study plan
      const { error: studyPlanError } = await supabase
        .from("study_plans")
        .insert({
          course_id: course.id,
          user_id: supabaseUser.id,
          title: studyPlan.title,
          description: studyPlan.description,
          milestones: studyPlan.milestones,
          start_date: new Date().toISOString().split("T")[0],
          target_completion_date: new Date(
            Date.now() + studyPlan.totalWeeks * 7 * 24 * 60 * 60 * 1000
          )
            .toISOString()
            .split("T")[0],
          status: "active",
        });

      if (studyPlanError) {
        console.error("Error saving study plan:", studyPlanError);
      }

      setProcessingStep("Generating flashcards...");
      const flashcardSets = await geminiService.generateFlashcards(
        courseAnalysis
      );

      // Save flashcards
      const flashcardsToInsert = flashcardSets.flatMap((set) =>
        set.cards.map((card) => ({
          course_id: course.id,
          user_id: supabaseUser.id,
          front: card.front,
          back: card.back,
          category: set.category,
          difficulty: card.difficulty,
          next_review_date: new Date().toISOString(),
          review_count: 0,
          ease_factor: 2.5,
          interval_days: 1,
        }))
      );

      if (flashcardsToInsert.length > 0) {
        const { error: flashcardsError } = await supabase
          .from("flashcards")
          .insert(flashcardsToInsert);

        if (flashcardsError) {
          console.error("Error saving flashcards:", flashcardsError);
        }
      }

      Alert.alert(
        "Success!",
        `Course "${courseAnalysis.title}" has been uploaded and processed successfully!`,
        [
          {
            text: "View Course",
            onPress: () => router.replace(`/course/${course.id}`),
          },
          {
            text: "Go to Dashboard",
            onPress: () => router.replace("/(tabs)"),
          },
        ]
      );
    } catch (error) {
      console.error("Error processing course:", error);
      Alert.alert(
        "Error",
        error instanceof Error ? error.message : "Failed to process course"
      );
    } finally {
      setIsProcessing(false);
      setProcessingStep("");
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Upload Course</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.title}>Upload Your Syllabus</Text>
        <Text style={styles.description}>
          Upload a course syllabus or learning material and we'll create a
          personalized study plan with flashcards using AI.
        </Text>

        <View style={styles.uploadSection}>
          <TouchableOpacity
            style={styles.uploadButton}
            onPress={pickDocument}
            disabled={isProcessing}
          >
            <Text style={styles.uploadButtonText}>
              {selectedFile ? "Change File" : "Select Document"}
            </Text>
          </TouchableOpacity>

          {selectedFile &&
            selectedFile.assets &&
            selectedFile.assets.length > 0 && (
              <View style={styles.fileInfo}>
                <Text style={styles.fileName}>
                  {selectedFile.assets[0].name}
                </Text>
                <Text style={styles.fileSize}>
                  {Math.round((selectedFile.assets[0].size || 0) / 1024)} KB
                </Text>
              </View>
            )}
        </View>

        <View style={styles.supportedFormats}>
          <Text style={styles.supportedTitle}>Supported Formats:</Text>
          <Text style={styles.supportedText}>PDF, DOC, DOCX, TXT</Text>
        </View>

        {selectedFile && (
          <TouchableOpacity
            style={[
              styles.processButton,
              isProcessing && styles.processButtonDisabled,
            ]}
            onPress={processCourse}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <View style={styles.processingContainer}>
                <ActivityIndicator color="#fff" size="small" />
                <Text style={styles.processButtonText}>{processingStep}</Text>
              </View>
            ) : (
              <Text style={styles.processButtonText}>Process Course</Text>
            )}
          </TouchableOpacity>
        )}

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>What happens next?</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoNumber}>1</Text>
            <Text style={styles.infoText}>AI analyzes your course content</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoNumber}>2</Text>
            <Text style={styles.infoText}>
              Generates a personalized study plan
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoNumber}>3</Text>
            <Text style={styles.infoText}>
              Creates flashcards for key concepts
            </Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoNumber}>4</Text>
            <Text style={styles.infoText}>
              Sets up spaced repetition schedule
            </Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  backButton: {
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 16,
    color: "#007AFF",
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    marginBottom: 30,
  },
  uploadSection: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 30,
    alignItems: "center",
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  uploadButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  uploadButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  fileInfo: {
    alignItems: "center",
  },
  fileName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 5,
  },
  fileSize: {
    fontSize: 14,
    color: "#666",
  },
  supportedFormats: {
    backgroundColor: "#fff",
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
  },
  supportedTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
    marginBottom: 5,
  },
  supportedText: {
    fontSize: 14,
    color: "#666",
  },
  processButton: {
    backgroundColor: "#34C759",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
    marginBottom: 30,
  },
  processButtonDisabled: {
    backgroundColor: "#ccc",
  },
  processButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  processingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoSection: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  infoNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#007AFF",
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
    textAlign: "center",
    lineHeight: 24,
    marginRight: 12,
  },
  infoText: {
    fontSize: 14,
    color: "#666",
    flex: 1,
  },
});
