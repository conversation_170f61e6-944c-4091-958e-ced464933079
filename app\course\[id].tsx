import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  Alert,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface Course {
  id: string;
  title: string;
  description: string;
  syllabus_content: string;
  difficulty_level: "beginner" | "intermediate" | "advanced";
  estimated_duration_weeks: number;
  created_at: string;
}

interface StudyPlan {
  id: string;
  title: string;
  description: string;
  milestones: any[];
  start_date: string;
  target_completion_date: string;
  status: "active" | "completed" | "paused";
}

interface Flashcard {
  id: string;
  front: string;
  back: string;
  category: string;
  difficulty: "easy" | "medium" | "hard";
  next_review_date: string;
  review_count: number;
}

export default function CourseDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useUser();
  const [course, setCourse] = useState<Course | null>(null);
  const [studyPlan, setStudyPlan] = useState<StudyPlan | null>(null);
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<
    "overview" | "plan" | "flashcards"
  >("overview");

  useEffect(() => {
    if (user && id) {
      loadCourseData();
    }
  }, [user, id]);

  const loadCourseData = async () => {
    try {
      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      // Load course
      const { data: courseData, error: courseError } = await supabase
        .from("courses")
        .select("*")
        .eq("id", id)
        .eq("user_id", supabaseUser.id)
        .single();

      if (courseError) {
        console.error("Error loading course:", courseError);
        Alert.alert("Error", "Course not found");
        router.back();
        return;
      }

      setCourse(courseData);

      // Load study plan
      const { data: planData } = await supabase
        .from("study_plans")
        .select("*")
        .eq("course_id", id)
        .eq("user_id", supabaseUser.id)
        .single();

      setStudyPlan(planData);

      // Load flashcards
      const { data: flashcardsData } = await supabase
        .from("flashcards")
        .select("*")
        .eq("course_id", id)
        .eq("user_id", supabaseUser.id)
        .order("created_at", { ascending: false });

      setFlashcards(flashcardsData || []);
    } catch (error) {
      console.error("Error loading course data:", error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCourseData();
    setRefreshing(false);
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case "beginner":
        return "#34C759";
      case "intermediate":
        return "#FF9500";
      case "advanced":
        return "#FF3B30";
      default:
        return "#007AFF";
    }
  };

  const getFlashcardsDue = () => {
    const now = new Date();
    return flashcards.filter((card) => new Date(card.next_review_date) <= now)
      .length;
  };

  const handleDeleteCourse = () => {
    Alert.alert(
      "Delete Course",
      "Are you sure you want to delete this course? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const { error } = await supabase
                .from("courses")
                .delete()
                .eq("id", id);

              if (error) throw error;

              Alert.alert("Success", "Course deleted successfully");
              router.back();
            } catch (error) {
              console.error("Error deleting course:", error);
              Alert.alert("Error", "Failed to delete course");
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading course...</Text>
      </View>
    );
  }

  if (!course) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>Course not found</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerBackButton}
          onPress={() => router.back()}
        >
          <Text style={styles.headerBackText}>← Back</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={handleDeleteCourse}
        >
          <Text style={styles.deleteButtonText}>Delete</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.courseHeader}>
          <Text style={styles.courseTitle}>{course.title}</Text>
          <View
            style={[
              styles.difficultyBadge,
              { backgroundColor: getDifficultyColor(course.difficulty_level) },
            ]}
          >
            <Text style={styles.difficultyText}>
              {course.difficulty_level.toUpperCase()}
            </Text>
          </View>
        </View>

        <Text style={styles.courseDescription}>{course.description}</Text>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {course.estimated_duration_weeks}
            </Text>
            <Text style={styles.statLabel}>Weeks</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{flashcards.length}</Text>
            <Text style={styles.statLabel}>Flashcards</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{getFlashcardsDue()}</Text>
            <Text style={styles.statLabel}>Due for Review</Text>
          </View>
        </View>

        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "overview" && styles.activeTab]}
            onPress={() => setActiveTab("overview")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "overview" && styles.activeTabText,
              ]}
            >
              Overview
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "plan" && styles.activeTab]}
            onPress={() => setActiveTab("plan")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "plan" && styles.activeTabText,
              ]}
            >
              Study Plan
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === "flashcards" && styles.activeTab]}
            onPress={() => setActiveTab("flashcards")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "flashcards" && styles.activeTabText,
              ]}
            >
              Flashcards
            </Text>
          </TouchableOpacity>
        </View>

        {activeTab === "overview" && (
          <View style={styles.tabContent}>
            <Text style={styles.sectionTitle}>Course Syllabus</Text>
            <View style={styles.syllabusContainer}>
              <Text style={styles.syllabusText}>{course.syllabus_content}</Text>
            </View>
          </View>
        )}

        {activeTab === "plan" && (
          <View style={styles.tabContent}>
            {studyPlan ? (
              <StudyPlanComponent
                studyPlan={studyPlan}
                courseId={id!}
                onProgressUpdate={loadCourseData}
              />
            ) : (
              <Text style={styles.noDataText}>No study plan available</Text>
            )}
          </View>
        )}

        {activeTab === "flashcards" && (
          <View style={styles.tabContent}>
            <View style={styles.flashcardActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push("/flashcard-review")}
                disabled={getFlashcardsDue() === 0}
              >
                <Text style={styles.actionButtonText}>
                  Review Cards ({getFlashcardsDue()})
                </Text>
              </TouchableOpacity>
            </View>
            {flashcards.length > 0 ? (
              flashcards.slice(0, 5).map((card) => (
                <View key={card.id} style={styles.flashcardPreview}>
                  <Text style={styles.flashcardFront}>{card.front}</Text>
                  <Text style={styles.flashcardCategory}>{card.category}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.noDataText}>No flashcards available</Text>
            )}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  headerBackButton: {
    padding: 5,
  },
  headerBackText: {
    fontSize: 16,
    color: "#007AFF",
  },
  deleteButton: {
    backgroundColor: "#FF3B30",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  deleteButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  errorText: {
    fontSize: 18,
    color: "#666",
    marginBottom: 20,
    textAlign: "center",
  },
  backButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  backButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  content: {
    flex: 1,
  },
  courseHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    padding: 20,
    backgroundColor: "#fff",
  },
  courseTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    flex: 1,
    marginRight: 15,
  },
  difficultyBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  difficultyText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  courseDescription: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    padding: 20,
    paddingTop: 0,
    backgroundColor: "#fff",
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    backgroundColor: "#fff",
    paddingVertical: 20,
    marginBottom: 10,
  },
  statItem: {
    alignItems: "center",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#007AFF",
    marginBottom: 5,
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    textAlign: "center",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    alignItems: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTab: {
    borderBottomColor: "#007AFF",
  },
  tabText: {
    fontSize: 16,
    color: "#666",
    fontWeight: "500",
  },
  activeTabText: {
    color: "#007AFF",
    fontWeight: "600",
  },
  tabContent: {
    backgroundColor: "#fff",
    padding: 20,
    minHeight: 300,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
  },
  syllabusContainer: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 15,
  },
  syllabusText: {
    fontSize: 14,
    color: "#333",
    lineHeight: 20,
  },
  planDescription: {
    fontSize: 16,
    color: "#666",
    lineHeight: 24,
    marginBottom: 15,
  },
  planDates: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  dateText: {
    fontSize: 14,
    color: "#666",
  },
  flashcardActions: {
    marginBottom: 20,
  },
  actionButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: "center",
  },
  actionButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  flashcardPreview: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 15,
    marginBottom: 10,
  },
  flashcardFront: {
    fontSize: 16,
    color: "#333",
    marginBottom: 5,
  },
  flashcardCategory: {
    fontSize: 12,
    color: "#666",
    fontStyle: "italic",
  },
  noDataText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginTop: 50,
  },
});
